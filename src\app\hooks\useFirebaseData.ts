import { useEffect, useState, useCallback } from 'react';
import { db } from '../firebase/config';
import { collection, getDocs, addDoc, updateDoc, deleteDoc, doc, UpdateData } from 'firebase/firestore';
import { Item } from '../types';
import { handleFirebaseError } from '../firebase/errorCodes';

const useFirebaseData = () => {
  const [items, setItems] = useState<Item[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const itemsCollectionRef = collection(db, 'products');

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await getDocs(itemsCollectionRef);
      const filteredData = data.docs.map(doc => ({ id: doc.id, ...doc.data() })) as Item[];
      setItems(filteredData);
    } catch (error) {
      const errorMessage = handleFirebaseError(error);
      console.error("Error fetching data: ", errorMessage);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [itemsCollectionRef]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const addItem = async (productData: {
    productType: string;
    imageUrl: string;
    productName: string;
    productDetails: string;
    storeRating: string;
    productReviewRating: string;
    soldCount: string;
    productLink1: string;
    productLink2: string;
    shopeeLink: string;
    lazadaLink: string;
    price: string;
    filterPrice: string;
    originalPrice: string;
    discountPercent: string;
    timestamp: string;
  }) => {
    try {
      setError(null);
      const docRef = await addDoc(itemsCollectionRef, productData); // บันทึกข้อมูลลงใน Firebase

      // Create a new item with the document ID
      const newItem = { id: docRef.id, ...productData } as Item;

      // Update the items state immediately with the new item at the beginning of the array
      // This ensures the newly added item appears at the top of the list
      setItems(prevItems => [newItem, ...prevItems]);

      console.log("New item added and state updated:", newItem);

      return newItem;
    } catch (error) {
      const errorMessage = handleFirebaseError(error);
      console.error("Error adding item: ", errorMessage);
      setError(errorMessage);
      return false;
    }
  };

  const updateItem = async (id: string, updatedData: Record<string, unknown>) => {
    try {
      setError(null); // Clear any previous errors

      // Validate and clean updatedData
      const validData: Record<string, unknown> = {};
      Object.entries(updatedData).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          validData[key] = value;
        }
      });

      if (Object.keys(validData).length === 0) {
        throw new Error("No valid fields to update.");
      }

      const itemRef = doc(db, 'products', id); // Reference to the document
      await updateDoc(itemRef, validData as UpdateData<Item>); // Update the document in Firestore
      await fetchData(); // Refresh the data after updating
      console.log(`Item with ID ${id} updated successfully.`);
      return true;
    } catch (error) {
      const errorMessage = handleFirebaseError(error); // Handle Firebase-specific errors
      console.error(`Error updating item with ID ${id}: ${errorMessage}`, {
        id,
        updatedData,
        rawError: error, // Include the raw error object for debugging
      });

      // Additional debugging for Firestore-specific issues
      if (error instanceof Error) {
        console.error("Firestore Error Details:", error.message);
      }

      setError(errorMessage); // Set the error state
      return false;
    }
  };

  const deleteItem = async (id: string) => {
    try {
      setError(null);
      const itemDoc = doc(db, 'products', id);
      await deleteDoc(itemDoc);
      await fetchData(); // Refresh data after deleting
      return true;
    } catch (error) {
      const errorMessage = handleFirebaseError(error);
      console.error("Error deleting item: ", errorMessage);
      setError(errorMessage);
      return false;
    }
  };

  return { items, loading, error, addItem, updateItem, deleteItem, fetchData };
};

export default useFirebaseData;