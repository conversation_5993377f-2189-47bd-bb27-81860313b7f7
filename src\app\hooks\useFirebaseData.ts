import { useEffect, useState, useCallback } from 'react';
import { db } from '../firebase/config';
import { collection, getDocs, addDoc, updateDoc, deleteDoc, doc, UpdateData } from 'firebase/firestore';
import { Item } from '../types';
import { handleFirebaseError } from '../firebase/errorCodes';

const useFirebaseData = () => {
  const [items, setItems] = useState<Item[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const itemsCollectionRef = collection(db, 'products');

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await getDocs(itemsCollectionRef);
      const filteredData = data.docs.map(doc => ({ id: doc.id, ...doc.data() })) as Item[];
      setItems(filteredData);
      console.log(`Successfully loaded ${filteredData.length} products`);
    } catch (error) {
      const errorMessage = handleFirebaseError(error);
      console.error("ไม่สามารถโหลดข้อมูลสินค้าได้:", errorMessage);

      // Set user-friendly error message in Thai
      let userFriendlyMessage = "ไม่สามารถโหลดข้อมูลสินค้าได้ กรุณาลองใหม่อีกครั้ง";

      if (errorMessage.includes('permission-denied')) {
        userFriendlyMessage = "ไม่มีสิทธิ์เข้าถึงข้อมูล กรุณาเข้าสู่ระบบใหม่";
      } else if (errorMessage.includes('unavailable')) {
        userFriendlyMessage = "เซิร์ฟเวอร์ไม่พร้อมใช้งาน กรุณาลองใหม่ในภายหลัง";
      } else if (errorMessage.includes('network')) {
        userFriendlyMessage = "ปัญหาการเชื่อมต่ออินเทอร์เน็ต กรุณาตรวจสอบการเชื่อมต่อ";
      }

      setError(userFriendlyMessage);
    } finally {
      setLoading(false);
    }
  }, [itemsCollectionRef]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const addItem = async (productData: {
    productType: string;
    imageUrl: string;
    productName: string;
    productDetails: string;
    storeRating: string;
    productReviewRating: string;
    soldCount: string;
    productLink1: string;
    productLink2: string;
    shopeeLink: string;
    lazadaLink: string;
    price: string;
    filterPrice: string;
    originalPrice: string;
    discountPercent: string;
    timestamp: string;
  }) => {
    try {
      setError(null);
      const docRef = await addDoc(itemsCollectionRef, productData); // บันทึกข้อมูลลงใน Firebase

      // Create a new item with the document ID
      const newItem = { id: docRef.id, ...productData } as Item;

      // Update the items state immediately with the new item at the beginning of the array
      // This ensures the newly added item appears at the top of the list
      setItems(prevItems => [newItem, ...prevItems]);

      console.log("New item added and state updated:", newItem);

      return newItem;
    } catch (error) {
      const errorMessage = handleFirebaseError(error);
      console.error("ไม่สามารถเพิ่มสินค้าได้:", errorMessage);

      // Set user-friendly error message in Thai
      let userFriendlyMessage = "ไม่สามารถเพิ่มสินค้าได้ กรุณาลองใหม่อีกครั้ง";

      if (errorMessage.includes('permission-denied')) {
        userFriendlyMessage = "ไม่มีสิทธิ์เพิ่มสินค้า กรุณาเข้าสู่ระบบใหม่";
      } else if (errorMessage.includes('unavailable')) {
        userFriendlyMessage = "เซิร์ฟเวอร์ไม่พร้อมใช้งาน กรุณาลองใหม่ในภายหลัง";
      } else if (errorMessage.includes('network')) {
        userFriendlyMessage = "ปัญหาการเชื่อมต่ออินเทอร์เน็ต กรุณาตรวจสอบการเชื่อมต่อ";
      }

      setError(userFriendlyMessage);
      return false;
    }
  };

  const updateItem = async (id: string, updatedData: Record<string, unknown>) => {
    try {
      setError(null); // Clear any previous errors

      // Validate and clean updatedData
      const validData: Record<string, unknown> = {};
      Object.entries(updatedData).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          validData[key] = value;
        }
      });

      if (Object.keys(validData).length === 0) {
        throw new Error("No valid fields to update.");
      }

      const itemRef = doc(db, 'products', id); // Reference to the document
      await updateDoc(itemRef, validData as UpdateData<Item>); // Update the document in Firestore
      await fetchData(); // Refresh the data after updating
      console.log(`Item with ID ${id} updated successfully.`);
      return true;
    } catch (error) {
      const errorMessage = handleFirebaseError(error); // Handle Firebase-specific errors
      console.error(`ไม่สามารถอัปเดตสินค้า ID ${id}:`, errorMessage);

      // Set user-friendly error message in Thai
      let userFriendlyMessage = "ไม่สามารถอัปเดตสินค้าได้ กรุณาลองใหม่อีกครั้ง";

      if (errorMessage.includes('permission-denied')) {
        userFriendlyMessage = "ไม่มีสิทธิ์แก้ไขสินค้า กรุณาเข้าสู่ระบบใหม่";
      } else if (errorMessage.includes('not-found')) {
        userFriendlyMessage = "ไม่พบสินค้าที่ต้องการแก้ไข";
      } else if (errorMessage.includes('unavailable')) {
        userFriendlyMessage = "เซิร์ฟเวอร์ไม่พร้อมใช้งาน กรุณาลองใหม่ในภายหลัง";
      } else if (errorMessage.includes('network')) {
        userFriendlyMessage = "ปัญหาการเชื่อมต่ออินเทอร์เน็ต กรุณาตรวจสอบการเชื่อมต่อ";
      }

      setError(userFriendlyMessage); // Set the error state
      return false;
    }
  };

  const deleteItem = async (id: string) => {
    try {
      setError(null);
      const itemDoc = doc(db, 'products', id);
      await deleteDoc(itemDoc);
      await fetchData(); // Refresh data after deleting
      return true;
    } catch (error) {
      const errorMessage = handleFirebaseError(error);
      console.error("ไม่สามารถลบสินค้าได้:", errorMessage);

      // Set user-friendly error message in Thai
      let userFriendlyMessage = "ไม่สามารถลบสินค้าได้ กรุณาลองใหม่อีกครั้ง";

      if (errorMessage.includes('permission-denied')) {
        userFriendlyMessage = "ไม่มีสิทธิ์ลบสินค้า กรุณาเข้าสู่ระบบใหม่";
      } else if (errorMessage.includes('not-found')) {
        userFriendlyMessage = "ไม่พบสินค้าที่ต้องการลบ";
      } else if (errorMessage.includes('unavailable')) {
        userFriendlyMessage = "เซิร์ฟเวอร์ไม่พร้อมใช้งาน กรุณาลองใหม่ในภายหลัง";
      } else if (errorMessage.includes('network')) {
        userFriendlyMessage = "ปัญหาการเชื่อมต่ออินเทอร์เน็ต กรุณาตรวจสอบการเชื่อมต่อ";
      }

      setError(userFriendlyMessage);
      return false;
    }
  };

  return { items, loading, error, addItem, updateItem, deleteItem, fetchData };
};

export default useFirebaseData;